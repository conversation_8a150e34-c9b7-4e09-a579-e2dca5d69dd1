# Arcaai EMR Microservice API Documentation

## Table of Contents
1. [Overview](#overview)
2. [Authentication](#authentication)
3. [Base URL](#base-url)
4. [Common Response Format](#common-response-format)
5. [Error Handling](#error-handling)
6. [API Endpoints](#api-endpoints)
   - [Authentication APIs](#authentication-apis)
   - [User Management APIs](#user-management-apis)
   - [Organization APIs](#organization-apis)
   - [Patient APIs](#patient-apis)
   - [Doctor APIs](#doctor-apis)
   - [Appointment APIs](#appointment-apis)
   - [Medicine APIs](#medicine-apis)
   - [Lab Test APIs](#lab-test-apis)
   - [Prescription APIs](#prescription-apis)
   - [Payment APIs](#payment-apis)
   - [ABDM Integration APIs](#abdm-integration-apis)
   - [Dashboard APIs](#dashboard-apis)
   - [Permission & Role APIs](#permission--role-apis)
   - [Patient Consultation APIs](#patient-consultation-apis)
   - [Patient Vitals APIs](#patient-vitals-apis)
   - [Patient Lifestyle APIs](#patient-lifestyle-apis)
   - [Patient Demographics APIs](#patient-demographics-apis)
   - [Patient History APIs](#patient-history-apis)
   - [Patient Diagnosis Notes APIs](#patient-diagnosis-notes-apis)
   - [Medical History Addiction APIs](#medical-history-addiction-apis)
   - [Lifestyle Question APIs](#lifestyle-question-apis)
   - [Lifestyle Ambient Listening APIs](#lifestyle-ambient-listening-apis)
   - [Lifestyle Summary APIs](#lifestyle-summary-apis)
   - [Customization APIs](#customization-apis)
   - [Consultant APIs](#consultant-apis)
   - [Nurse APIs](#nurse-apis)
   - [Queue Management APIs](#queue-management-apis)
   - [Test Package APIs](#test-package-apis)
   - [Prescription Package APIs](#prescription-package-apis)
   - [Lab Report Document APIs](#lab-report-document-apis)
   - [Document Upload APIs](#document-upload-apis)
   - [Proxy APIs](#proxy-apis)
   - [Summary APIs](#summary-apis)

## Overview

The Arcaai EMR (Electronic Medical Records) Microservice provides a comprehensive API for managing healthcare data including patients, doctors, appointments, prescriptions, lab tests, payments, ABDM integration, and organizational information. The system supports role-based access control with different permission levels for various user types.

## Authentication

The API uses JWT (JSON Web Token) based authentication with role-based access control.

### Authentication Header
```
Authorization: Bearer <jwt_token>
```

### User Roles
- **Super Admin**: Full system access
- **Organization Admin**: Organization-level management
- **Doctor**: Medical professional access
- **Nurse**: Nursing staff access
- **Receptionist**: Front desk operations

## Base URL
```
https://emr-ms-dev-apim.azure-api.net/EMR-MS/api/v0.1
```

## Common Response Format

### Success Response
```json
{
  "data": {},
  "message": "Success message",
  "status": 200
}
```

### Error Response

```json
{
  "error": "Error message",
  "status": 400|401|403|404|500
}
```

## Error Handling

| Status Code | Description |
|-------------|-------------|
| 400 | Bad Request - Invalid input parameters |
| 401 | Unauthorized - Missing or invalid authentication |
| 403 | Forbidden - Insufficient permissions |
| 404 | Not Found - Resource not found |
| 500 | Internal Server Error - Server-side error |

## API Endpoints

### Authentication APIs

#### Login
**POST** `/auth/login`

Authenticate user and receive JWT token.

**Request Body:**
```json
{
  "email": "<EMAIL>",
  "password": "password123"
}
```

**Response:**
```json
{
  "user": {
    "id": "user-id",
    "email": "<EMAIL>",
    "name": "User Name",
    "userRole": "doctor",
    "organizationId": "org-id",
    "organizationName": "Hospital Name"
  },
  "token": "jwt-token",
  "redirectUrl": "https://app.com/emr",
  "permissionKeys": ["emr.access", "emr.patientinfo.view"]
}
```

#### Reset Password
**POST** `/auth/reset-password`

Reset user password using reset token.

**Request Body:**
```json
{
  "token": "reset-token",
  "newPassword": "newPassword123"
}
```

### User Management APIs

#### Get User Details
**GET** `/user?email={email}`
**GET** `/user?userType={userType}`

Retrieve user information by email or user type.

**Query Parameters:**
- `email` (string): User email address
- `userType` (string): Filter by user type (doctor, nurse, etc.)

**Response:**
```json
{
  "id": "user-id",
  "email": "<EMAIL>",
  "name": "User Name",
  "userRole": "doctor",
  "organizationId": "org-id",
  "organizationName": "Hospital Name",
  "isActive": true
}
```

#### Create User
**POST** `/user`

Create a new user account.

**Request Body:**
```json
{
  "email": "<EMAIL>",
  "firstName": "John",
  "lastName": "Doe",
  "userRole": "doctor",
  "organizationId": "org-id",
  "phone": "+**********",
  "address": {
    "street": "123 Main St",
    "city": "City Name",
    "state": "State",
    "zipCode": "12345",
    "country": "Country"
  },
  "dateOfBirth": "1990-01-01",
  "gender": "male"
}
```

**Response:**
```json
{
  "data": {
    "id": "user-123",
    "email": "<EMAIL>",
    "firstName": "John",
    "lastName": "Doe",
    "userRole": "doctor",
    "organizationId": "org-id",
    "organizationName": "Hospital Name",
    "phone": "+**********",
    "isActive": true,
    "createdAt": "2024-01-01T10:00:00Z",
    "updatedAt": "2024-01-01T10:00:00Z"
  },
  "message": "User created successfully",
  "status": 201
}
```

#### Update User
**PATCH** `/user?userId={userId}`

Update user information (email and organizationId cannot be changed).

**Request Body:**
```json
{
  "firstName": "Updated John",
  "lastName": "Updated Doe",
  "phone": "+**********",
  "isActive": true,
  "address": {
    "street": "456 Updated St",
    "city": "Updated City",
    "state": "Updated State",
    "zipCode": "54321",
    "country": "Updated Country"
  }
}
```

**Response:**
```json
{
  "data": {
    "id": "user-123",
    "email": "<EMAIL>",
    "firstName": "Updated John",
    "lastName": "Updated Doe",
    "phone": "+**********",
    "isActive": true,
    "updatedAt": "2024-01-01T11:00:00Z"
  },
  "message": "User updated successfully",
  "status": 200
}
```

#### Delete User
**DELETE** `/user?userId={userId}`

Delete a user from both the local database and Azure B2C.

**Query Parameters:**
- `userId` (string, required): The ID of the user to delete

**Response:**
```json
{
  "message": "User deleted successfully",
  "userId": "user-id"
}
```

**Error Responses:**
- `400 Bad Request`: Missing userId parameter
- `403 Forbidden`: Super Admin users cannot be deleted
- `404 Not Found`: User not found
- `500 Internal Server Error`: Failed to delete user

**Notes:**
- Super Admin users cannot be deleted for security reasons
- The user will be deleted from both the local Cosmos DB container and Azure B2C
- If the user doesn't exist in Azure B2C but exists locally, only the local deletion will be performed
- If the user has no B2C ID, only local deletion will be performed
- This operation cannot be undone

#### Get Users List
**GET** `/user/list`

Get paginated list of users with filtering options.

**Query Parameters:**
- `organizationId` (string, optional): Filter by organization
- `search` (string, optional): Search by name or email
- `role` (string, optional): Filter by user role
- `isActive` (string, optional): Filter by active status
- `sortBy` (string, optional): Sort field (default: name)
- `sortOrder` (string, optional): Sort order (asc/desc)
- `pageSize` (number, optional): Items per page (default: 10)
- `page` (number, optional): Page number (default: 1)
- `continueToken` (string, optional): Pagination token

**Response:**
```json
{
  "data": {
    "users": [
      {
        "id": "user-123",
        "email": "<EMAIL>",
        "firstName": "Dr. John",
        "lastName": "Smith",
        "userRole": "doctor",
        "organizationId": "org-456",
        "organizationName": "City Hospital",
        "phone": "+**********",
        "isActive": true,
        "createdAt": "2024-01-01T10:00:00Z",
        "lastLoginAt": "2024-01-15T09:30:00Z"
      }
    ],
    "pagination": {
      "totalCount": 150,
      "pageSize": 10,
      "currentPage": 1,
      "totalPages": 15,
      "hasMoreResults": true,
      "continueToken": "token-123"
    }
  },
  "status": 200
}
```

#### Set Password
**POST** `/user/set-password`

Set password for user account.

**Request Body:**
```json
{
  "email": "<EMAIL>",
  "password": "newPassword123",
  "confirmPassword": "newPassword123"
}
```

**Response:**
```json
{
  "data": {
    "message": "Password set successfully",
    "email": "<EMAIL>"
  },
  "status": 200
}
```

### Organization APIs

#### List Organizations
**GET** `/list-organizations`

Get list of all organizations.

**Query Parameters:**
- `pageSize` (number, optional): Items per page (default: 10)
- `continueToken` (string, optional): Pagination token
- `search` (string, optional): Search by organization name
- `isActive` (boolean, optional): Filter by active status

**Response:**
```json
{
  "data": {
    "organizations": [
      {
        "id": "org-123",
        "name": "City Hospital",
        "contactEmail": "<EMAIL>",
        "contactPersonName": "Dr. Admin",
        "contactPhone": "+**********",
        "address": {
          "street": "123 Hospital St",
          "city": "Medical City",
          "state": "State",
          "zipCode": "12345",
          "country": "Country"
        },
        "isActive": true,
        "totalUsers": 45,
        "totalPatients": 1250,
        "createdAt": "2024-01-01T10:00:00Z"
      }
    ],
    "pagination": {
      "totalCount": 25,
      "pageSize": 10,
      "hasMoreResults": true,
      "continueToken": "token-456"
    }
  },
  "status": 200
}
```

#### Get Organization
**GET** `/organization?id={organizationId}`

Get organization details by ID.

**Response:**
```json
{
  "data": {
    "id": "org-123",
    "name": "City Hospital",
    "contactEmail": "<EMAIL>",
    "contactPersonName": "Dr. Admin",
    "contactPhone": "+**********",
    "address": {
      "street": "123 Hospital St",
      "city": "Medical City",
      "state": "State",
      "zipCode": "12345",
      "country": "Country"
    },
    "description": "Leading healthcare provider",
    "isActive": true,
    "settings": {
      "timezone": "UTC",
      "currency": "USD",
      "language": "en"
    },
    "statistics": {
      "totalUsers": 45,
      "totalPatients": 1250,
      "totalDoctors": 15,
      "totalAppointments": 5600
    },
    "createdAt": "2024-01-01T10:00:00Z",
    "updatedAt": "2024-01-15T14:30:00Z"
  },
  "status": 200
}
```

#### Create Organization
**POST** `/organization`

Create a new organization with default roles and permissions.

**Request Body:**
```json
{
  "name": "New Hospital",
  "contactEmail": "<EMAIL>",
  "contactPersonName": "Dr. Administrator",
  "contactPhone": "+**********",
  "address": {
    "street": "456 Medical Ave",
    "city": "Healthcare City",
    "state": "Medical State",
    "zipCode": "54321",
    "country": "Country"
  },
  "description": "Modern healthcare facility",
  "settings": {
    "timezone": "UTC",
    "currency": "USD",
    "language": "en"
  },
  "adminUser": {
    "firstName": "Admin",
    "lastName": "User",
    "email": "<EMAIL>",
    "phone": "+**********"
  }
}
```

**Response:**
```json
{
  "data": {
    "id": "org-789",
    "name": "New Hospital",
    "contactEmail": "<EMAIL>",
    "contactPersonName": "Dr. Administrator",
    "isActive": true,
    "defaultRoles": [
      {
        "id": "role-123",
        "name": "Organization Super Admin",
        "permissions": ["emr.access", "mrd.access", "admin.access"]
      }
    ],
    "adminUser": {
      "id": "user-456",
      "email": "<EMAIL>",
      "role": "Organization Super Admin"
    },
    "createdAt": "2024-01-01T10:00:00Z"
  },
  "message": "Organization created successfully with default roles and admin user",
  "status": 201
}
```

#### Update Organization
**PATCH** `/organization?id={organizationId}`

Update organization information (email uniqueness is validated excluding current organization).

**Request Body:**
```json
{
  "name": "Updated Hospital Name",
  "contactPersonName": "Updated Contact Person",
  "contactPhone": "+9876543210",
  "address": {
    "street": "789 Updated St",
    "city": "Updated City",
    "state": "Updated State",
    "zipCode": "98765",
    "country": "Updated Country"
  },
  "description": "Updated description",
  "settings": {
    "timezone": "EST",
    "currency": "USD",
    "language": "en"
  },
  "isActive": true
}
```

**Response:**
```json
{
  "data": {
    "id": "org-123",
    "name": "Updated Hospital Name",
    "contactPersonName": "Updated Contact Person",
    "updatedAt": "2024-01-15T16:00:00Z"
  },
  "message": "Organization updated successfully",
  "status": 200
}
```

#### Delete Organization
**DELETE** `/organization?id={organizationId}`

Delete an organization and all associated data.

**Response:**
```json
{
  "data": {
    "id": "org-123",
    "deletedAt": "2024-01-15T17:00:00Z"
  },
  "message": "Organization deleted successfully",
  "status": 200
}
```

### Patient APIs

#### Get Patient
**GET** `/patient?id={patientId}`

Get patient profile by ID.

**Query Parameters:**
- `id` (string, required): Patient ID
- `last_consultation_date` (string, optional): Filter by consultation date

**Response:**
```json
{
  "data": {
    "id": "patient-123",
    "name": "John Doe",
    "sex": "Male",
    "dob": "1990-01-01",
    "age": 34,
    "height": "175 cm",
    "weight": "70 kg",
    "bmi": 22.9,
    "address": "123 Patient Street, City, State 12345",
    "aadhar": "1234-5678-9012",
    "abha": "ABHA-123456789",
    "contact": {
      "phone": "+**********",
      "email": "<EMAIL>",
      "emergencyContact": {
        "name": "Jane Doe",
        "relationship": "Spouse",
        "phone": "+**********"
      }
    },
    "insurance": {
      "provider": "Health Insurance Co.",
      "policyNumber": "INS-123456",
      "validUntil": "2024-12-31",
      "coverageAmount": 500000
    },
    "medicalHistory": {
      "allergies": ["Penicillin", "Peanuts"],
      "chronicConditions": ["Diabetes Type 2"],
      "currentMedications": ["Metformin 500mg"]
    },
    "organizationId": "org-456",
    "createdAt": "2024-01-01T10:00:00Z",
    "lastConsultationDate": "2024-01-10T14:30:00Z"
  },
  "status": 200
}
```

#### Create Patient
**POST** `/patient`

Create a new patient record.

**Request Body:**
```json
{
  "name": "John Doe",
  "sex": "Male",
  "dob": "1990-01-01",
  "height": "175",
  "weight": "70",
  "address": "123 Patient Street, City, State 12345",
  "aadhar": "1234-5678-9012",
  "abha": "ABHA-123456789",
  "contact": {
    "phone": "+**********",
    "email": "<EMAIL>",
    "emergencyContact": {
      "name": "Jane Doe",
      "relationship": "Spouse",
      "phone": "+**********"
    }
  },
  "insurance": {
    "provider": "Health Insurance Co.",
    "policyNumber": "INS-123456",
    "validUntil": "2024-12-31",
    "coverageAmount": 500000,
    "proofDocument": "insurance-proof-url"
  },
  "medicalHistory": {
    "allergies": ["Penicillin"],
    "chronicConditions": ["Diabetes Type 2"],
    "familyHistory": {
      "diabetes": true,
      "heartDisease": false,
      "cancer": false
    }
  }
}
```

**Response:**
```json
{
  "data": {
    "id": "patient-123",
    "name": "John Doe",
    "patientId": "PAT-2024-001",
    "organizationId": "org-456",
    "createdAt": "2024-01-01T10:00:00Z"
  },
  "message": "Patient created successfully",
  "status": 201
}
```

#### Update Patient
**PUT** `/patient`
**PATCH** `/patient`

Update patient information.

**Request Body:**
```json
{
  "id": "patient-123",
  "name": "John Updated Doe",
  "weight": "72",
  "address": "456 Updated Street, New City, State 54321",
  "contact": {
    "phone": "+**********",
    "email": "<EMAIL>"
  },
  "insurance": {
    "provider": "New Insurance Co.",
    "policyNumber": "INS-789456",
    "validUntil": "2025-12-31"
  }
}
```

**Response:**
```json
{
  "data": {
    "id": "patient-123",
    "name": "John Updated Doe",
    "updatedAt": "2024-01-15T11:00:00Z"
  },
  "message": "Patient updated successfully",
  "status": 200
}
```

#### Search Patients
**POST** `/patient/search`

Search patients with pagination.

**Request Body:**
```json
{
  "query": "John Doe",
  "pagesize": 10,
  "continuetoken": "pagination-token-123"
}
```

**Response:**
```json
{
  "data": {
    "patients": [
      {
        "id": "patient-123",
        "name": "John Doe",
        "age": 34,
        "sex": "Male",
        "phone": "+**********",
        "lastConsultationDate": "2024-01-10T14:30:00Z",
        "organizationId": "org-456"
      }
    ],
    "pagination": {
      "totalFetched": 1,
      "pageSize": 10,
      "hasMoreResults": false,
      "continueToken": null
    }
  },
  "status": 200
}
```

#### Get Organization Patients
**GET** `/organization/patients`

Get patients for a specific organization with pagination.

**Query Parameters:**
- `organizationId` (string): Organization ID
- `search` (string, optional): Search term
- `pageSize` (number, optional): Items per page (default: 10)
- `continueToken` (string, optional): Pagination token

**Response:**
```json
{
  "data": {
    "patients": [
      {
        "id": "patient-123",
        "name": "John Doe",
        "age": 34,
        "sex": "Male",
        "phone": "+**********",
        "email": "<EMAIL>",
        "lastConsultationDate": "2024-01-10T14:30:00Z",
        "totalConsultations": 5,
        "organizationId": "org-456",
        "status": "active"
      }
    ],
    "pagination": {
      "totalCount": 1250,
      "pageSize": 10,
      "currentPage": 1,
      "totalPages": 125,
      "hasMoreResults": true,
      "continueToken": "token-789"
    }
  },
  "status": 200
}
```

### Doctor APIs

#### Get Doctor
**GET** `/doctor?id={doctorId}`
**GET** `/doctor?email={email}`

Get doctor profile by ID or email.

**Response:**
```json
{
  "data": {
    "id": "doctor-123",
    "username": "doctor123",
    "general": {
      "fullName": "Dr. John Smith",
      "designation": "Senior Cardiologist",
      "department": "Cardiology",
      "doctorID": "DOC-001",
      "contactNumber": "+**********",
      "workEmail": "<EMAIL>",
      "profilePicture": "https://storage.com/profile-pic.jpg"
    },
    "personalDetails": {
      "dateOfBirth": "1980-01-01",
      "gender": "Male",
      "nationality": "American",
      "religion": "Christian",
      "maritalStatus": "Married",
      "address": "123 Doctor Lane, Medical City"
    },
    "professionalDetails": {
      "specialties": ["Cardiology", "Interventional Cardiology"],
      "qualifications": [
        {
          "degree": "MBBS",
          "specialization": "General Medicine",
          "university": "Harvard Medical School",
          "yearOfCompletion": "2005"
        },
        {
          "degree": "MD",
          "specialization": "Cardiology",
          "university": "Johns Hopkins",
          "yearOfCompletion": "2008"
        }
      ],
      "experience": 16,
      "licenseNumber": "MD-12345",
      "boardCertifications": ["American Board of Cardiology"]
    },
    "availability": {
      "workingHours": {
        "monday": "09:00-17:00",
        "tuesday": "09:00-17:00",
        "wednesday": "09:00-17:00",
        "thursday": "09:00-17:00",
        "friday": "09:00-17:00"
      },
      "consultationFee": 500
    },
    "organizationId": "org-456",
    "isActive": true,
    "createdAt": "2024-01-01T10:00:00Z",
    "updatedAt": "2024-01-15T14:30:00Z"
  },
  "status": 200
}
```

#### Create Doctor
**POST** `/doctor`

Create a new doctor profile.

**Request Body:**
```json
{
  "username": "doctor123",
  "general": {
    "fullName": "Dr. John Smith",
    "designation": "Cardiologist",
    "department": "Cardiology",
    "doctorID": "DOC-001",
    "contactNumber": "+**********",
    "workEmail": "<EMAIL>"
  },
  "personalDetails": {
    "dateOfBirth": "1980-01-01",
    "gender": "Male",
    "nationality": "American",
    "religion": "Christian",
    "maritalStatus": "Married",
    "address": "123 Doctor Lane, Medical City"
  },
  "professionalDetails": {
    "specialties": ["Cardiology"],
    "qualifications": [
      {
        "degree": "MBBS",
        "specialization": "General Medicine",
        "university": "Harvard Medical School",
        "yearOfCompletion": "2005"
      }
    ],
    "experience": 16,
    "licenseNumber": "MD-12345",
    "boardCertifications": ["American Board of Cardiology"]
  },
  "availability": {
    "workingHours": {
      "monday": "09:00-17:00",
      "tuesday": "09:00-17:00",
      "wednesday": "09:00-17:00",
      "thursday": "09:00-17:00",
      "friday": "09:00-17:00"
    },
    "consultationFee": 500
  }
}
```

**Response:**
```json
{
  "data": {
    "id": "doctor-123",
    "username": "doctor123",
    "general": {
      "fullName": "Dr. John Smith",
      "doctorID": "DOC-001"
    },
    "organizationId": "org-456",
    "createdAt": "2024-01-01T10:00:00Z"
  },
  "message": "Doctor profile created successfully",
  "status": 201
}
```

#### Update Doctor
**PUT** `/doctor`
**PATCH** `/doctor?id={doctorId}`

Update doctor profile information.

**Request Body:**
```json
{
  "id": "doctor-123",
  "general": {
    "fullName": "Dr. John Updated Smith",
    "designation": "Senior Cardiologist",
    "contactNumber": "+**********"
  },
  "professionalDetails": {
    "experience": 17,
    "qualifications": [
      {
        "degree": "MBBS",
        "specialization": "General Medicine",
        "university": "Harvard Medical School",
        "yearOfCompletion": "2005"
      },
      {
        "degree": "MD",
        "specialization": "Cardiology",
        "university": "Johns Hopkins",
        "yearOfCompletion": "2008"
      }
    ]
  },
  "availability": {
    "consultationFee": 550
  }
}
```

**Response:**
```json
{
  "data": {
    "id": "doctor-123",
    "updatedAt": "2024-01-15T16:00:00Z"
  },
  "message": "Doctor profile updated successfully",
  "status": 200
}
```

Update doctor profile information.

#### Delete Doctor
**DELETE** `/doctor?id={doctorId}`

Delete a doctor profile and associated user account.

**Response:**
```json
{
  "data": {
    "id": "doctor-123",
    "deletedAt": "2024-01-15T17:00:00Z"
  },
  "message": "Doctor profile and user account deleted successfully",
  "status": 200
}
```

#### Upload Doctor Profile Picture
**POST** `/doctor/profile-picture/upload`

Upload profile picture for a doctor (max 5MB).

**Request Body (multipart/form-data):**
```
file: [profile-picture.jpg]
doctorId: doctor-123
```

**Response:**
```json
{
  "data": {
    "profilePictureUrl": "https://storage.blob.core.windows.net/profile-pictures/doctor-123/profile.jpg",
    "uploadedAt": "2024-01-15T12:00:00Z"
  },
  "message": "Profile picture uploaded successfully",
  "status": 200
}
```

#### Get/Delete Doctor Profile Picture URL
**GET** `/doctor/profile-picture/url?doctorId={doctorId}`
**DELETE** `/doctor/profile-picture/url?doctorId={doctorId}`

Retrieve or delete the URL for a doctor's profile picture.

**Query Parameters:**
- `doctorId` (string, required): The ID of the doctor

**GET Response:**
```json
{
  "data": {
    "profilePictureUrl": "https://storage.blob.core.windows.net/profile-pictures/doctor-123/profile.jpg"
  },
  "status": 200
}
```

**DELETE Response:**
```json
{
  "data": {
    "message": "Profile picture deleted successfully",
    "doctorId": "doctor-123"
  },
  "status": 200
}
```

### Appointment APIs

#### Get Appointments
**GET** `/appointment?doctorId={doctorId}`
**GET** `/appointment?doctorId={doctorId}&date={date}`

Get appointments for a doctor, optionally filtered by date.

**Query Parameters:**
- `doctorId` (string, required): Doctor ID
- `date` (string, optional): Filter by specific date (YYYY-MM-DD)
- `patientId` (string, optional): Filter by patient ID

**Response:**
```json
{
  "data": {
    "appointments": [
      {
        "id": "appointment-123",
        "patientId": "patient-456",
        "patientName": "John Doe",
        "doctorId": "doctor-789",
        "doctorName": "Dr. Smith",
        "appointmentDate": "2024-01-15",
        "appointmentTime": "10:00",
        "duration": 30,
        "reason": "Regular checkup",
        "status": "scheduled",
        "type": "consultation",
        "notes": "Follow-up appointment",
        "createdAt": "2024-01-10T09:00:00Z",
        "updatedAt": "2024-01-10T09:00:00Z"
      }
    ],
    "totalCount": 15,
    "date": "2024-01-15"
  },
  "status": 200
}
```

#### Create Appointment
**POST** `/appointment?doctorId={doctorId}`

Create a new appointment.

**Request Body:**
```json
{
  "patientId": "patient-456",
  "appointmentDate": "2024-01-15",
  "appointmentTime": "10:00",
  "duration": 30,
  "reason": "Regular checkup",
  "type": "consultation",
  "priority": "normal",
  "notes": "Patient requested morning slot",
  "reminderSettings": {
    "sms": true,
    "email": true,
    "reminderTime": 24
  }
}
```

**Response:**
```json
{
  "data": {
    "id": "appointment-123",
    "patientId": "patient-456",
    "doctorId": "doctor-789",
    "appointmentDate": "2024-01-15",
    "appointmentTime": "10:00",
    "status": "scheduled",
    "appointmentNumber": "APT-2024-001",
    "createdAt": "2024-01-10T09:00:00Z"
  },
  "message": "Appointment created successfully",
  "status": 201
}
```

#### Update Appointment
**PUT** `/appointment?doctorId={doctorId}`
**PATCH** `/appointment?appointmentId={appointmentId}`

Update appointment details.

**Request Body:**
```json
{
  "appointmentDate": "2024-01-16",
  "appointmentTime": "11:00",
  "duration": 45,
  "reason": "Follow-up consultation",
  "status": "confirmed",
  "notes": "Patient confirmed availability",
  "reminderSettings": {
    "sms": true,
    "email": true,
    "reminderTime": 12
  }
}
```

**Response:**
```json
{
  "data": {
    "id": "appointment-123",
    "appointmentDate": "2024-01-16",
    "appointmentTime": "11:00",
    "status": "confirmed",
    "updatedAt": "2024-01-10T10:00:00Z"
  },
  "message": "Appointment updated successfully",
  "status": 200
}
```

#### Delete Appointment
**DELETE** `/appointment?queueId={queueId}`

Delete an appointment from the queue.

**Response:**
```json
{
  "data": {
    "queueId": "queue-123",
    "deletedAt": "2024-01-10T11:00:00Z"
  },
  "message": "Appointment deleted successfully",
  "status": 200
}
```

### Medicine APIs

#### Get All Medicines
**GET** `/medicine`

Get list of all medicines.

**Response:**
```json
{
  "data": [
    {
      "id": "medicine-123",
      "name": "Paracetamol",
      "genericName": "Acetaminophen",
      "brandName": "Tylenol",
      "manufacturer": "Johnson & Johnson",
      "category": "Analgesic",
      "dosageForm": "Tablet",
      "strength": "500mg",
      "packSize": "10 tablets",
      "price": 25.50,
      "description": "Pain reliever and fever reducer",
      "sideEffects": ["Nausea", "Dizziness"],
      "contraindications": ["Liver disease"],
      "isActive": true
    }
  ],
  "status": 200
}
```

#### Seed Medicines
**POST** `/medicine`

Seed medicines from Excel file.

**Response:**
```json
{
  "data": {
    "totalProcessed": 1500,
    "successCount": 1450,
    "errorCount": 50,
    "errors": [
      {
        "row": 15,
        "error": "Invalid dosage format"
      }
    ]
  },
  "message": "Medicines seeded successfully",
  "status": 200
}
```

#### Search Medicines
**POST** `/medicine/search`
**POST** `/medicine/search?organizationId={organizationId}`

Search medicines with pagination. Supports both general medicine search and organization-specific search.

**Query Parameters:**
- `organizationId` (string, optional): When provided, searches in OrganizationMedicines container for organization-specific medicines

**Request Body:**
```json
{
  "searchText": "paracetamol",
  "pageSize": 10,
  "continuationToken": "token-123",
  "filters": {
    "category": "Analgesic",
    "dosageForm": "Tablet",
    "priceRange": {
      "min": 10,
      "max": 100
    }
  }
}
```

**Response:**
```json
{
  "data": {
    "medicines": [
      {
        "id": "medicine-123",
        "name": "Paracetamol",
        "genericName": "Acetaminophen",
        "brandName": "Tylenol",
        "strength": "500mg",
        "dosageForm": "Tablet",
        "price": 25.50,
        "organizationPrice": 23.00,
        "isActive": true,
        "isOrganizationSpecific": true
      }
    ],
    "pagination": {
      "totalFetched": 1,
      "pageSize": 10,
      "hasMoreResults": false,
      "continuationToken": null
    }
  },
  "status": 200
}
```

**Behavior:**
- Without `organizationId`: Searches in the general medicines container
- With `organizationId`:
  1. Gets active medicine IDs from OrganizationMedicines container for the organization
  2. Searches in the medicines container using those medicine IDs and the search text
  3. Returns only medicines that are both assigned to the organization and match the search criteria

#### Get Organization Medicines
**GET** `/organization/medicines`

Get medicines available for an organization.

**Query Parameters:**
- `organizationId` (string): Organization ID
- `search` (string, optional): Search term
- `isActive` (boolean, optional): Filter by active status
- `pageSize` (number, optional): Items per page
- `continueToken` (string, optional): Pagination token

**Response:**
```json
{
  "data": {
    "medicines": [
      {
        "id": "medicine-123",
        "name": "Paracetamol",
        "genericName": "Acetaminophen",
        "strength": "500mg",
        "organizationPrice": 23.00,
        "standardPrice": 25.50,
        "isActive": true,
        "addedDate": "2024-01-01T10:00:00Z"
      }
    ],
    "pagination": {
      "totalCount": 450,
      "pageSize": 10,
      "hasMoreResults": true,
      "continueToken": "token-456"
    }
  },
  "status": 200
}
```

### Lab Test APIs

#### Get Lab Tests
**GET** `/lab-tests`

Get list of all lab tests.

**Response:**
```json
{
  "data": [
    {
      "id": "test-123",
      "name": "Complete Blood Count",
      "shortName": "CBC",
      "category": "Hematology",
      "department": "HEMATOLOGY",
      "loincCode": "58410-2",
      "description": "Comprehensive blood analysis",
      "sampleType": "Blood",
      "sampleVolume": "5ml",
      "preparationInstructions": "12 hour fasting required",
      "normalRange": {
        "male": "4.5-5.5 million cells/mcL",
        "female": "4.0-5.0 million cells/mcL"
      },
      "price": 150.00,
      "reportingTime": "24 hours",
      "isActive": true
    }
  ],
  "status": 200
}
```

#### Add Lab Test
**POST** `/lab-tests`

Add a new lab test.

**Request Body:**
```json
{
  "name": "Lipid Profile",
  "shortName": "LP",
  "category": "Biochemistry",
  "department": "CHEMISTRY",
  "loincCode": "24331-1",
  "description": "Cholesterol and triglyceride levels",
  "sampleType": "Blood",
  "sampleVolume": "3ml",
  "preparationInstructions": "12 hour fasting required",
  "normalRange": {
    "totalCholesterol": "<200 mg/dL",
    "ldl": "<100 mg/dL",
    "hdl": ">40 mg/dL (men), >50 mg/dL (women)",
    "triglycerides": "<150 mg/dL"
  },
  "price": 200.00,
  "reportingTime": "24 hours",
  "methodology": "Enzymatic method"
}
```

**Response:**
```json
{
  "data": {
    "id": "test-456",
    "name": "Lipid Profile",
    "shortName": "LP",
    "loincCode": "24331-1",
    "createdAt": "2024-01-01T10:00:00Z"
  },
  "message": "Lab test added successfully",
  "status": 201
}
```

#### Search Lab Tests
**POST** `/lab-tests/search`
**POST** `/lab-tests/search?organizationId={organizationId}`

Search lab tests with filters. Supports both general lab test search and organization-specific search.

**Query Parameters:**
- `organizationId` (string, optional): When provided, searches in OrganizationTests container for organization-specific lab tests

**Request Body:**
```json
{
  "searchText": "blood test",
  "pageSize": 20,
  "continuationToken": "token-123",
  "department": "HEMATOLOGY",
  "category": "Hematology",
  "priceRange": {
    "min": 100,
    "max": 500
  }
}
```

**Response:**
```json
{
  "data": {
    "tests": [
      {
        "id": "test-123",
        "name": "Complete Blood Count",
        "shortName": "CBC",
        "category": "Hematology",
        "department": "HEMATOLOGY",
        "loincCode": "58410-2",
        "price": 150.00,
        "organizationPrice": 140.00,
        "reportingTime": "24 hours",
        "isActive": true,
        "isOrganizationSpecific": true
      }
    ],
    "pagination": {
      "totalFetched": 1,
      "pageSize": 20,
      "hasMoreResults": false,
      "continuationToken": null
    }
  },
  "status": 200
}
```

**Behavior:**
- Without `organizationId`: Searches in the general lab_tests container
- With `organizationId`:
  1. Gets active test IDs from OrganizationTests container for the organization
  2. Searches in the lab_tests container using those test IDs and the search criteria
  3. Returns only lab tests that are both assigned to the organization and match the search criteria
  4. Includes organization-specific pricing and department information

#### Get Departments
**GET** `/lab-test/departments`

Get list of lab test departments.

**Response:**
```json
{
  "data": {
    "departments": [
      {
        "id": "dept-1",
        "name": "HEMATOLOGY",
        "displayName": "Hematology",
        "description": "Blood-related tests",
        "testCount": 45,
        "isActive": true
      },
      {
        "id": "dept-2",
        "name": "CHEMISTRY",
        "displayName": "Clinical Chemistry",
        "description": "Biochemical analysis",
        "testCount": 120,
        "isActive": true
      }
    ]
  },
  "status": 200
}
```

#### Get LOINC List
**GET** `/loinc/list`

Get paginated LOINC test list.

**Query Parameters:**
- `pageSize` (number, optional): Items per page (default: 10)
- `continueToken` (string, optional): Pagination token
- `isActive` (boolean, optional): Filter by active status

**Response:**
```json
{
  "data": {
    "tests": [
      {
        "id": "loinc-123",
        "loincCode": "58410-2",
        "name": "Complete Blood Count",
        "shortName": "CBC",
        "component": "Blood cells",
        "property": "Count",
        "timeAspect": "Point in time",
        "system": "Blood",
        "scaleType": "Quantitative",
        "methodType": "Automated count",
        "class": "HEMATOLOGY",
        "isActive": true
      }
    ],
    "pagination": {
      "totalCount": 15000,
      "pageSize": 10,
      "hasMoreResults": true,
      "continueToken": "token-789"
    }
  },
  "status": 200
}
```

#### Update LOINC Tests
**POST** `/loinc/update`

Update organization test details (bulk operation).

**Request Body:**
```json
{
  "organizationId": "org-123",
  "selectAll": true,
  "department": "Clinical Chemistry",
  "testIds": ["test-1", "test-2", "test-3"],
  "updateData": {
    "isActive": true,
    "organizationPrice": 150.00,
    "reportingTime": "24 hours"
  },
  "async": true
}
```

**Response (Async):**
```json
{
  "data": {
    "message": "Organization test update started",
    "jobId": "job-456",
    "async": true,
    "statusUrl": "/api/loinc/update/status/job-456",
    "estimatedCompletion": "2024-01-01T10:05:00Z"
  },
  "status": 202
}
```

**Response (Sync - small batches):**
```json
{
  "data": {
    "totalProcessed": 150,
    "successCount": 148,
    "errorCount": 2,
    "errors": [
      {
        "testId": "test-5",
        "error": "Invalid price format"
      }
    ]
  },
  "message": "LOINC tests updated successfully",
  "status": 200
}
```

#### Get Update Status
**GET** `/loinc/update/status/{jobId}`

Get status of bulk update operation.

**Response:**
```json
{
  "data": {
    "jobId": "job-456",
    "status": "completed",
    "progress": 100,
    "totalRecords": 1000,
    "processedRecords": 1000,
    "successCount": 995,
    "errorCount": 5,
    "startTime": "2024-01-01T10:00:00Z",
    "completionTime": "2024-01-01T10:04:30Z",
    "errors": [
      {
        "testId": "test-10",
        "error": "Duplicate LOINC code"
      }
    ]
  },
  "status": 200
}
```

#### Get LOINC Tests for Organization
**GET** `/loinc/tests-for-organization`

Get LOINC tests configured for an organization.

**Query Parameters:**
- `organizationId` (string, required): Organization ID
- `department` (string, optional): Filter by department
- `isActive` (boolean, optional): Filter by active status
- `pageSize` (number, optional): Items per page
- `continueToken` (string, optional): Pagination token

**Response:**
```json
{
  "data": {
    "tests": [
      {
        "id": "org-test-123",
        "testId": "test-456",
        "loincCode": "58410-2",
        "name": "Complete Blood Count",
        "department": "HEMATOLOGY",
        "standardPrice": 150.00,
        "organizationPrice": 140.00,
        "isActive": true,
        "addedDate": "2024-01-01T10:00:00Z"
      }
    ],
    "pagination": {
      "totalCount": 250,
      "pageSize": 10,
      "hasMoreResults": true,
      "continueToken": "token-org-123"
    }
  },
  "status": 200
}
```

### Prescription APIs

#### Get Prescriptions
**GET** `/prescriptions`

Get patient prescriptions with pagination.

**Query Parameters:**
- `patientId` (string, required): Patient ID
- `doctorId` (string, optional): Filter by doctor
- `pageSize` (number, optional): Items per page (default: 10)
- `continueToken` (string, optional): Pagination token
- `status` (string, optional): Filter by status (active, completed, cancelled)
- `dateFrom` (string, optional): Start date filter
- `dateTo` (string, optional): End date filter

**Response:**
```json
{
  "data": {
    "prescriptions": [
      {
        "id": "prescription-123",
        "patientId": "patient-456",
        "patientName": "John Doe",
        "doctorId": "doctor-789",
        "doctorName": "Dr. Smith",
        "prescriptionDate": "2024-01-15",
        "status": "active",
        "totalAmount": 450.00,
        "medicines": [
          {
            "medicineId": "medicine-123",
            "medicineName": "Paracetamol",
            "dosage": "500mg",
            "frequency": "Twice daily",
            "duration": "7 days",
            "quantity": 14,
            "instructions": "Take after meals"
          }
        ],
        "createdAt": "2024-01-15T10:00:00Z"
      }
    ],
    "pagination": {
      "totalCount": 25,
      "pageSize": 10,
      "hasMoreResults": true,
      "continueToken": "token-prescription-123"
    }
  },
  "status": 200
}
```

#### Get Prescription Details
**GET** `/prescriptions/details`

Get detailed prescription information including PDF generation.

**Query Parameters:**
- `prescriptionId` (string, required): Prescription ID
- `format` (string, optional): Response format (json, pdf)

**Response:**
```json
{
  "data": {
    "id": "prescription-123",
    "prescriptionNumber": "RX-2024-001",
    "patientDetails": {
      "id": "patient-456",
      "name": "John Doe",
      "age": 34,
      "gender": "Male",
      "phone": "+**********"
    },
    "doctorDetails": {
      "id": "doctor-789",
      "name": "Dr. Smith",
      "specialization": "Cardiologist",
      "licenseNumber": "MD-12345",
      "signature": "doctor-signature-url"
    },
    "organizationDetails": {
      "name": "City Hospital",
      "address": "123 Hospital St, Medical City",
      "phone": "+**********",
      "logo": "hospital-logo-url"
    },
    "prescriptionDate": "2024-01-15",
    "medicines": [
      {
        "medicineId": "medicine-123",
        "medicineName": "Paracetamol",
        "genericName": "Acetaminophen",
        "dosage": "500mg",
        "frequency": "Twice daily",
        "duration": "7 days",
        "quantity": 14,
        "unitPrice": 25.50,
        "totalPrice": 357.00,
        "instructions": "Take after meals",
        "productForm": "Tab"
      }
    ],
    "totalAmount": 450.00,
    "instructions": "Complete the full course of medication",
    "followUpDate": "2024-01-22",
    "status": "active",
    "pdfUrl": "https://storage.com/prescriptions/prescription-123.pdf"
  },
  "status": 200
}
```

#### Create/Update Prescription
**POST** `/prescriptions`
**PATCH** `/prescriptions`

Create new prescription or update existing one.

**Request Body:**
```json
{
  "patientId": "patient-456",
  "doctorId": "doctor-789",
  "prescriptionDate": "2024-01-15",
  "medicines": [
    {
      "medicineId": "medicine-123",
      "dosage": "500mg",
      "frequency": "Twice daily",
      "duration": "7 days",
      "quantity": 14,
      "instructions": "Take after meals"
    },
    {
      "medicineId": "medicine-456",
      "dosage": "10mg",
      "frequency": "Once daily",
      "duration": "30 days",
      "quantity": 30,
      "instructions": "Take before breakfast"
    }
  ],
  "generalInstructions": "Complete the full course of medication",
  "followUpDate": "2024-01-22",
  "symptoms": ["Fever", "Headache"],
  "diagnosis": "Viral fever"
}
```

**Response:**
```json
{
  "data": {
    "id": "prescription-123",
    "prescriptionNumber": "RX-2024-001",
    "patientId": "patient-456",
    "doctorId": "doctor-789",
    "totalAmount": 450.00,
    "status": "active",
    "pdfUrl": "https://storage.com/prescriptions/prescription-123.pdf",
    "createdAt": "2024-01-15T10:00:00Z"
  },
  "message": "Prescription created successfully",
  "status": 201
}
```

#### Search Prescriptions
**POST** `/prescriptions/search`

Search prescriptions with advanced filters.

**Request Body:**
```json
{
  "searchText": "John Doe",
  "filters": {
    "doctorId": "doctor-789",
    "status": "active",
    "dateFrom": "2024-01-01",
    "dateTo": "2024-01-31",
    "medicineId": "medicine-123"
  },
  "pageSize": 10,
  "continueToken": "search-token-123"
}
```

**Response:**
```json
{
  "data": {
    "prescriptions": [
      {
        "id": "prescription-123",
        "prescriptionNumber": "RX-2024-001",
        "patientName": "John Doe",
        "doctorName": "Dr. Smith",
        "prescriptionDate": "2024-01-15",
        "totalAmount": 450.00,
        "status": "active",
        "medicineCount": 2
      }
    ],
    "pagination": {
      "totalFetched": 1,
      "pageSize": 10,
      "hasMoreResults": false,
      "continueToken": null
    }
  },
  "status": 200
}
```

### Dashboard APIs

#### Get Dashboard Summary
**GET** `/dashboard/summary`

Get dashboard summary statistics.

**Query Parameters:**
- `organizationId` (string, optional): Filter by organization
- `dateRange` (string, optional): Date range filter

### Permission & Role APIs

#### Get Permissions by Role
**GET** `/permissions/api-list`

Get API permissions for a role.

**Query Parameters:**
- `roleId` (string): Role ID

#### Assign Permissions
**POST** `/assign-permissions`

Assign permissions to a role.

**Request Body:**
```json
{
  "roleId": "role-id",
  "permissions": ["permission1", "permission2"]
}
```

#### List Roles
**GET** `/list-roles`

Get list of all roles.

#### Create Role
**POST** `/role`

Create a new role.

#### Update Role
**PATCH** `/role`

Update role information.

#### Delete Role
**DELETE** `/role`

Delete a role.

## Rate Limiting

The API implements rate limiting to prevent abuse. Current limits:
- 1000 requests per hour per user
- Bulk operations have separate limits

## Pagination

Most list endpoints support pagination using continuation tokens:

**Request:**
```
GET /api/endpoint?pageSize=10&continueToken=token
```

**Response:**
```json
{
  "data": [...],
  "pagination": {
    "pageSize": 10,
    "continueToken": "next-token",
    "hasMore": true,
    "totalCount": 100
  }
}
```

## Webhooks

The system supports webhooks for real-time notifications:
- Patient updates
- Appointment changes
- Lab result availability

## SDK and Libraries

Official SDKs available for:
- JavaScript/Node.js
- Python
- C#/.NET

### Patient Consultation APIs

#### Get Patient Consultations
**GET** `/patient-consultation?patientId={patientId}`

Get consultation history for a patient.

#### Create Patient Consultation
**POST** `/patient-consultation?patientId={patientId}`

Create a new consultation record.

**Request Body:**
```json
{
  "doctorId": "doctor-id",
  "consultationDate": "2024-01-01",
  "chiefComplaint": "Patient complaint",
  "diagnosis": "Diagnosis details",
  "treatment": "Treatment plan",
  "notes": "Additional notes"
}
```

#### Update Patient Consultation
**PUT** `/patient-consultation?patientId={patientId}`

Update consultation record.

### Patient Vitals APIs

#### Get Patient Vitals
**GET** `/patient/vitals?patientId={patientId}`

Get vital signs for a patient.

#### Create/Update Patient Vitals
**POST** `/patient/vitals?patientId={patientId}`
**PUT** `/patient/vitals?patientId={patientId}`

Record patient vital signs.

**Request Body:**
```json
{
  "height": "175",
  "weight": "70",
  "bloodPressure": {
    "systolic": 120,
    "diastolic": 80
  },
  "pulse": 72,
  "temperature": 98.6,
  "respiratoryRate": 16,
  "oxygenSaturation": 98,
  "bmi": 22.9,
  "recordedDate": "2024-01-01T10:00:00Z"
}
```

### Patient History APIs

#### Get Patient History
**GET** `/patient/history?patientId={patientId}`

Get patient medical history.

#### Create/Update Patient History
**POST** `/patient/history?patientId={patientId}`
**PUT** `/patient/history?patientId={patientId}`

Record patient medical history.

### Patient Lifestyle APIs

#### Get Patient Lifestyle
**GET** `/patient/lifestyle?patientId={patientId}`

Get patient lifestyle information.

#### Create/Update Patient Lifestyle
**POST** `/patient/lifestyle?patientId={patientId}`
**PATCH** `/patient/lifestyle?patientId={patientId}`

Record patient lifestyle data.

#### Get Lifestyle Questions
**GET** `/lifestyle/question`

Get available lifestyle assessment questions.

#### Create Lifestyle Question
**POST** `/lifestyle/question`

Create new lifestyle assessment question.

#### Lifestyle Ambient Listening
**POST** `/lifestyle/ambient-listening`

Process conversation transcript for lifestyle assessment using dynamic questions from database.

**Request Body:**
```json
{
  "transcript": "Doctor: How often do you exercise? Patient: I try to go to the gym 3 times a week...",
  "source": "lifestyle-assessment"
}
```

**Response:**
```json
{
  "conversation": [
    {
      "speaker": "doctor",
      "message": "How many major meals do you eat in a day?"
    },
    {
      "speaker": "patient",
      "message": "Usually three meals."
    }
  ],
  "summary": {
    "questions": [
      {
        "id": "food_intake",
        "title": "Food intake patterns",
        "icon": "material-symbols:dining-outline",
        "fields": [
          {
            "id": "major_meals_per_day",
            "label": "Number of major meals per day",
            "type": "radio",
            "options": ["1", "2", "3", "4", "Other"],
            "allowOtherSpecify": true,
            "modal": {
              "title": "Meals per day",
              "inputType": "number"
            },
            "value": "3"
          },
          {
            "id": "taste_rating",
            "label": "How do you rate the taste of your meals?",
            "type": "slider",
            "min": 0,
            "max": 10,
            "step": 1,
            "description": "(0=Unpleasant to 10=Delicious)",
            "value": 7
          }
        ]
      }
    ]
  }
}
```

### Patient Lab Test APIs

#### Get Patient Lab Tests
**GET** `/patient-lab-test?patientId={patientId}`

Get lab tests for a patient.

#### Create Patient Lab Test
**POST** `/patient-lab-test?patientId={patientId}`

Order lab tests for a patient.

**Request Body:**
```json
{
  "tests": [
    {
      "testId": "test-id",
      "testName": "Complete Blood Count",
      "department": "Hematology",
      "urgency": "routine",
      "instructions": "Fasting required"
    }
  ],
  "orderedBy": "doctor-id",
  "orderDate": "2024-01-01"
}
```

#### Get Lab Test Details
**GET** `/patient-lab-test/details?testId={testId}`

Get detailed lab test information.

### Nurse APIs

#### Get Nurse Profile
**GET** `/nurse?id={nurseId}`

Get nurse profile information.

#### Create Nurse Profile
**POST** `/nurse`

Create a new nurse profile.

**Request Body:**
```json
{
  "personalInfo": {
    "fullName": "Nurse Name",
    "employeeId": "NUR-001",
    "department": "ICU",
    "contactNumber": "+**********",
    "email": "<EMAIL>"
  },
  "qualifications": [
    {
      "degree": "BSN",
      "institution": "Nursing College",
      "yearOfCompletion": "2020"
    }
  ],
  "experience": [
    {
      "hospital": "Previous Hospital",
      "department": "Emergency",
      "duration": "2 years"
    }
  ]
}
```

#### Update Nurse Profile
**PUT** `/nurse`

Update nurse profile information.

### Queue Management APIs

#### Get Queue
**GET** `/queue`

Get patient queue information.

**Query Parameters:**
- `departmentId` (string, optional): Filter by department
- `doctorId` (string, optional): Filter by doctor
- `date` (string, optional): Filter by date

#### Add to Queue
**POST** `/queue`

Add patient to queue.

**Request Body:**
```json
{
  "patientId": "patient-id",
  "doctorId": "doctor-id",
  "appointmentId": "appointment-id",
  "priority": "normal",
  "estimatedTime": "10:30"
}
```

#### Update Queue Status
**PATCH** `/queue?queueId={queueId}`

Update patient queue status.

#### Update Queue By ID
**PATCH** `/book-consultation/queue?queueId={queueId}`

Update queue information by queue ID.

**Query Parameters:**
- `queueId` (string, required): The ID of the queue to update.

**Request Body:**
```json
{
  "time": "value1",
  "date": "value2"
}
```

### Appointment Queue APIs

#### Get Appointment Queue
**GET** `/appointment/queue`

Get appointment queue for a specific date/doctor.

### Test Package APIs

#### Get Test Packages
**GET** `/package`

Get available test packages.

#### Create Test Package
**POST** `/package`

Create a new test package.

**Request Body:**
```json
{
  "name": "Basic Health Package",
  "type": "department",
  "tests": [
    {
      "testId": "test-1",
      "testName": "Complete Blood Count"
    },
    {
      "testId": "test-2",
      "testName": "Lipid Profile"
    }
  ],
  "price": 1500,
  "description": "Basic health screening package"
}
```

#### Update Test Package
**PATCH** `/package?packageId={packageId}`

Update test package information.

#### Get Tests for Package
**GET** `/package/tests?packageId={packageId}`

Get tests included in a specific package.

### Prescription Package APIs

#### Get Prescription Packages
**GET** `/prescription-package`

Get available prescription packages.

#### Create Prescription Package
**POST** `/prescription-package`

Create a new prescription package.

### Lab Report Document APIs

#### Upload Lab Report
**POST** `/lab-report/upload`

Upload lab report document.

**Request Body (multipart/form-data):**
```
file: [lab-report.pdf]
patientId: patient-id
testId: test-id
reportDate: 2024-01-01
```

#### Preview Lab Report
**GET** `/lab-report/preview?reportId={reportId}`

Preview uploaded lab report.

### Doctor Document Upload APIs

#### Upload Doctor Document
**POST** `/doctor/document/upload`

Upload doctor-related documents.

### Summary APIs

#### Get Summary
**GET** `/summary`

Get various summary reports.

**Query Parameters:**
- `type` (string): Summary type (patients, appointments, revenue)
- `organizationId` (string, optional): Filter by organization
- `dateRange` (string, optional): Date range filter

### Customization APIs

#### Get EMR Customization
**GET** `/customise-emr`

Get EMR customization settings.

#### Update EMR Customization
**POST** `/customise-emr`

Update EMR customization settings.

#### Get Doctor EMR Customization
**GET** `/doctor-customise-emr`

Get doctor-specific EMR customization.

#### Update Doctor EMR Customization
**POST** `/doctor-customise-emr`

Update doctor-specific EMR customization.

### Consultant APIs

#### Get Consultants
**GET** `/consultant`

Get list of consultants.

#### Create Consultant
**POST** `/consultant`

Create a new consultant profile.

### Proxy APIs

#### ICD Proxy
**GET** `/icd-proxy`

Proxy for ICD (International Classification of Diseases) API.

#### SNOMED Proxy
**GET** `/snomed-proxy`

Proxy for SNOMED CT terminology API.

### Patient Diagnosis Notes APIs

#### Get Diagnosis Notes
**GET** `/patient/diagnosis-notes?patientId={patientId}`

Get diagnosis notes for a patient.

#### Create Diagnosis Notes
**POST** `/patient/diagnosis-notes?patientId={patientId}`

Create diagnosis notes.

#### Update Diagnosis Notes
**PATCH** `/patient/diagnosis-notes?patientId={patientId}`

Update diagnosis notes.

### Patient Lifestyle Notes APIs

#### Get Lifestyle Notes
**GET** `/patient/lifestyle/note?patientId={patientId}`

Get lifestyle notes for a patient.

#### Create Lifestyle Notes
**POST** `/patient/lifestyle/note?patientId={patientId}`

Create lifestyle notes.

#### Update Lifestyle Notes
**PATCH** `/patient/lifestyle/note?patientId={patientId}`

Update lifestyle notes.

### Patient Medical History Addiction APIs

#### Get Patient Medical History Addiction
**GET** `/patient/lifestyle/medical-history-addiction?patientId={patientId}`

Get medical history addiction information for a patient including substance use history, diagnosis records, and nicotine dependence testing.

**Query Parameters:**
- `patientId` (string, required): The ID of the patient

**Response:**
```json
{
  "id": "medical-history-id",
  "patientId": "patient-id",
  "diagnosis": [
    {
      "diseaseName": "Hypertension",
      "yearOfDiagnosis": "2020",
      "diagnosisDuration": "3 Years",
      "status": "active",
      "treatmentHistory": "Medication and lifestyle changes"
    }
  ],
  "smoking": {
    "history": "former",
    "count": "10",
    "frequency": "daily"
  },
  "alcohol": {
    "history": "current",
    "count": "2-3",
    "frequency": "2-3_times_week"
  },
  "tobacco": {
    "history": "no"
  },
  "drugs": {
    "history": "no"
  },
  "nicotineDependenceTest": {
    "id": "test-id",
    "responses": {
      "timeToFirstCigarette": "Within 5 minutes",
      "findDifficult": "Yes",
      "whichCigarette": "First one in the morning",
      "cigarettesPerDay": "21-30",
      "moreFrequentMorning": "Yes",
      "smokeWhenIll": "Yes"
    },
    "testDate": "2024-01-15T10:00:00Z"
  },
  "created_on": "2024-01-01T10:00:00Z",
  "updated_on": "2024-01-15T14:30:00Z",
  "create_by": "user-id",
  "update_by": "user-id"
}
```

**Error Responses:**
- `400 Bad Request`: Missing patientId parameter
- `404 Not Found`: No medical history found for the patient
- `500 Internal Server Error`: Server error

#### Create Patient Medical History Addiction
**POST** `/patient/lifestyle/medical-history-addiction?patientId={patientId}`

Create medical history addiction record for a patient.

**Query Parameters:**
- `patientId` (string, required): The ID of the patient

**Request Body:**
```json
{
  "diagnosis": [
    {
      "diseaseName": "Hypertension",
      "yearOfDiagnosis": "2020",
      "diagnosisDuration": "3 Years",
      "status": "active",
      "treatmentHistory": "Medication and lifestyle changes"
    }
  ],
  "smoking": {
    "history": "former",
    "count": "10",
    "frequency": "daily"
  },
  "alcohol": {
    "history": "current",
    "count": "2-3",
    "frequency": "2-3_times_week"
  },
  "tobacco": {
    "history": "no"
  },
  "drugs": {
    "history": "no"
  },
  "nicotineDependenceTest": {
    "responses": {
      "timeToFirstCigarette": "Within 5 minutes",
      "findDifficult": "Yes",
      "whichCigarette": "First one in the morning",
      "cigarettesPerDay": "21-30",
      "moreFrequentMorning": "Yes",
      "smokeWhenIll": "Yes"
    },
    "testDate": "2024-01-15T10:00:00Z"
  }
}
```

**Response:**
```json
{
  "id": "medical-history-id",
  "patientId": "patient-id",
  "diagnosis": [
    {
      "diseaseName": "Hypertension",
      "yearOfDiagnosis": "2020",
      "diagnosisDuration": "3 Years",
      "status": "active",
      "treatmentHistory": "Medication and lifestyle changes"
    }
  ],
  "smoking": {
    "history": "former",
    "count": "10",
    "frequency": "daily"
  },
  "alcohol": {
    "history": "current",
    "count": "2-3",
    "frequency": "2-3_times_week"
  },
  "tobacco": {
    "history": "no"
  },
  "drugs": {
    "history": "no"
  },
  "nicotineDependenceTest": {
    "id": "test-id",
    "responses": {
      "timeToFirstCigarette": "Within 5 minutes",
      "findDifficult": "Yes",
      "whichCigarette": "First one in the morning",
      "cigarettesPerDay": "21-30",
      "moreFrequentMorning": "Yes",
      "smokeWhenIll": "Yes"
    },
    "testDate": "2024-01-15T10:00:00Z"
  },
  "created_on": "2024-01-01T10:00:00Z",
  "updated_on": "2024-01-01T10:00:00Z",
  "create_by": "user-id",
  "update_by": "user-id"
}
```

**Error Responses:**
- `400 Bad Request`: Missing patientId parameter or invalid request body
- `409 Conflict`: Medical history addiction record already exists for this patient
- `500 Internal Server Error`: Server error

#### Update Patient Medical History Addiction
**PUT** `/patient/lifestyle/medical-history-addiction?id={medicalHistoryId}`

Update existing medical history addiction record.

**Query Parameters:**
- `id` (string, required): The ID of the medical history addiction record to update

**Request Body:**
```json
{
  "diagnosis": [
    {
      "diseaseName": "Hypertension",
      "yearOfDiagnosis": "2020",
      "diagnosisDuration": "4 Years",
      "status": "active",
      "treatmentHistory": "Updated medication and lifestyle changes"
    },
    {
      "diseaseName": "Diabetes Type 2",
      "yearOfDiagnosis": "2023",
      "diagnosisDuration": "1 Year",
      "status": "active",
      "treatmentHistory": "Diet control and medication"
    }
  ],
  "smoking": {
    "history": "no"
  },
  "alcohol": {
    "history": "occasionally",
    "count": "1-2",
    "frequency": "4_times_month"
  },
  "tobacco": {
    "history": "no"
  },
  "drugs": {
    "history": "no"
  },
  "nicotineDependenceTest": null
}
```

**Response:**
```json
{
  "id": "medical-history-id",
  "patientId": "patient-id",
  "diagnosis": [
    {
      "diseaseName": "Hypertension",
      "yearOfDiagnosis": "2020",
      "diagnosisDuration": "4 Years",
      "status": "active",
      "treatmentHistory": "Updated medication and lifestyle changes"
    },
    {
      "diseaseName": "Diabetes Type 2",
      "yearOfDiagnosis": "2023",
      "diagnosisDuration": "1 Year",
      "status": "active",
      "treatmentHistory": "Diet control and medication"
    }
  ],
  "smoking": {
    "history": "no"
  },
  "alcohol": {
    "history": "occasionally",
    "count": "1-2",
    "frequency": "4_times_month"
  },
  "tobacco": {
    "history": "no"
  },
  "drugs": {
    "history": "no"
  },
  "nicotineDependenceTest": null,
  "created_on": "2024-01-01T10:00:00Z",
  "updated_on": "2024-01-20T16:45:00Z",
  "create_by": "user-id",
  "update_by": "user-id"
}
```

**Error Responses:**
- `400 Bad Request`: Missing id parameter or invalid request body
- `404 Not Found`: Medical history addiction record not found
- `500 Internal Server Error`: Server error

#### Delete Patient Medical History Addiction
**DELETE** `/patient/lifestyle/medical-history-addiction?id={medicalHistoryId}`

Delete a medical history addiction record.

**Query Parameters:**
- `id` (string, required): The ID of the medical history addiction record to delete

**Response:**
```json
{
  "message": "Medical history addiction record deleted successfully",
  "id": "medical-history-id"
}
```

**Error Responses:**
- `400 Bad Request`: Missing id parameter
- `404 Not Found`: Medical history addiction record not found
- `500 Internal Server Error`: Server error

**Notes:**
- The diagnosis array can contain multiple diagnosis records for comprehensive medical history
- Substance use history supports three states: "no", "former", and "current"
- Frequency options include: "daily", "2-3_times_week", "4_times_month", "occasionally"
- Nicotine dependence test is optional and only relevant for patients with smoking history
- All substance use fields (smoking, alcohol, tobacco, drugs) follow the same structure
- The API supports full CRUD operations for comprehensive medical history management

### Patient Demographics APIs

#### Get Patient Demographics
**GET** `/patient/lifestyle/demographics?patientId={patientId}`

Get demographic information for a patient.

**Query Parameters:**
- `patientId` (string, required): The ID of the patient

**Response:**
```json
{
  "id": "demographics-id",
  "patientId": "patient-id",
  "age": 35,
  "gender": "Male",
  "ethnicity": "Asian",
  "maritalStatus": "Married",
  "occupation": "Software Engineer",
  "education": "Bachelor's Degree",
  "income": "50000-75000",
  "address": {
    "street": "123 Main St",
    "city": "City",
    "state": "State",
    "zipCode": "12345"
  },
  "emergencyContact": {
    "name": "Emergency Contact Name",
    "relationship": "Spouse",
    "phone": "+**********"
  },
  "created_on": "2024-01-01T10:00:00Z",
  "updated_on": "2024-01-01T10:00:00Z",
  "create_by": "user-id",
  "update_by": "user-id"
}
```

**Error Responses:**
- `400 Bad Request`: Missing patientId parameter
- `404 Not Found`: Patient demographics not found

#### Create Patient Demographics
**POST** `/patient/lifestyle/demographics?patientId={patientId}`

Create demographic information for a patient.

**Query Parameters:**
- `patientId` (string, required): The ID of the patient

**Request Body:**
```json
{
  "age": 35,
  "gender": "Male",
  "ethnicity": "Asian",
  "maritalStatus": "Married",
  "occupation": "Software Engineer",
  "education": "Bachelor's Degree",
  "income": "50000-75000",
  "address": {
    "street": "123 Main St",
    "city": "City",
    "state": "State",
    "zipCode": "12345"
  },
  "emergencyContact": {
    "name": "Emergency Contact Name",
    "relationship": "Spouse",
    "phone": "+**********"
  }
}
```

**Response:**
```json
{
  "id": "demographics-id",
  "patientId": "patient-id",
  "age": 35,
  "gender": "Male",
  "ethnicity": "Asian",
  "maritalStatus": "Married",
  "occupation": "Software Engineer",
  "education": "Bachelor's Degree",
  "income": "50000-75000",
  "address": {
    "street": "123 Main St",
    "city": "City",
    "state": "State",
    "zipCode": "12345"
  },
  "emergencyContact": {
    "name": "Emergency Contact Name",
    "relationship": "Spouse",
    "phone": "+**********"
  },
  "created_on": "2024-01-01T10:00:00Z",
  "updated_on": "2024-01-01T10:00:00Z",
  "create_by": "user-id",
  "update_by": "user-id"
}
```

**Error Responses:**
- `400 Bad Request`: Missing patientId parameter or invalid request body
- `409 Conflict`: Demographics already exists for this patient
- `400 Bad Request`: Validation errors in the request data

#### Update Patient Demographics
**PUT** `/patient/lifestyle/demographics?id={demographicsId}`

Update existing demographic information.

**Query Parameters:**
- `id` (string, required): The ID of the demographics record to update

**Request Body:**
```json
{
  "age": 36,
  "gender": "Male",
  "ethnicity": "Asian",
  "maritalStatus": "Married",
  "occupation": "Senior Software Engineer",
  "education": "Master's Degree",
  "income": "75000-100000",
  "address": {
    "street": "456 Oak Ave",
    "city": "New City",
    "state": "New State",
    "zipCode": "54321"
  },
  "emergencyContact": {
    "name": "Updated Emergency Contact",
    "relationship": "Spouse",
    "phone": "+**********"
  }
}
```

**Response:**
```json
{
  "id": "demographics-id",
  "patientId": "patient-id",
  "age": 36,
  "gender": "Male",
  "ethnicity": "Asian",
  "maritalStatus": "Married",
  "occupation": "Senior Software Engineer",
  "education": "Master's Degree",
  "income": "75000-100000",
  "address": {
    "street": "456 Oak Ave",
    "city": "New City",
    "state": "New State",
    "zipCode": "54321"
  },
  "emergencyContact": {
    "name": "Updated Emergency Contact",
    "relationship": "Spouse",
    "phone": "+**********"
  },
  "created_on": "2024-01-01T10:00:00Z",
  "updated_on": "2024-01-15T14:30:00Z",
  "create_by": "user-id",
  "update_by": "user-id"
}
```

**Error Responses:**
- `400 Bad Request`: Missing id parameter or invalid request body
- `404 Not Found`: Demographics record not found
- `400 Bad Request`: Validation errors in the request data

## Data Models

### Patient Model
```json
{
  "id": "string",
  "name": "string",
  "sex": "Male|Female|Other",
  "dob": "YYYY-MM-DD",
  "height": "string",
  "weight": "string",
  "address": "string",
  "aadhar": "string",
  "abha": "string",
  "contact": {
    "phone": "string",
    "email": "string"
  },
  "insurance": {
    "provider": "string",
    "id": "string",
    "proof": "string"
  },
  "organizationId": "string",
  "createdAt": "ISO 8601 datetime",
  "updatedAt": "ISO 8601 datetime"
}
```

### Doctor Model
```json
{
  "id": "string",
  "username": "string",
  "general": {
    "fullName": "string",
    "designation": "string",
    "department": "string",
    "doctorID": "string",
    "contactNumber": "string",
    "workEmail": "string"
  },
  "personalDetails": {
    "dateOfBirth": "YYYY-MM-DD",
    "gender": "Male|Female|Other",
    "nationality": "string",
    "religion": "string",
    "maritalStatus": "string"
  },
  "professionalDetails": {
    "specialties": ["string"],
    "qualifications": [
      {
        "degree": "string",
        "specialization": "string",
        "university": "string",
        "institute": "string",
        "yearOfCompletion": "string"
      }
    ],
    "certifications": [
      {
        "name": "string",
        "regNumber": "string",
        "validFrom": "YYYY-MM-DD",
        "validTo": "YYYY-MM-DD"
      }
    ],
    "experience": [
      {
        "hospitalName": "string",
        "department": "string",
        "designation": "string",
        "from": "YYYY-MM-DD",
        "to": "YYYY-MM-DD"
      }
    ]
  }
}
```

### Organization Model
```json
{
  "id": "string",
  "name": "string",
  "contactEmail": "string",
  "contactPersonName": "string",
  "contactPhone": "string",
  "address": {
    "street": "string",
    "city": "string",
    "state": "string",
    "zipCode": "string",
    "country": "string"
  },
  "description": "string",
  "isActive": "boolean",
  "createdAt": "ISO 8601 datetime",
  "updatedAt": "ISO 8601 datetime"
}
```

### Test Model (LOINC)
```json
{
  "id": "string",
  "LOINC_NUM": "string",
  "COMPONENT": "string",
  "PROPERTY": "string",
  "TIME_ASPCT": "string",
  "SYSTEM": "string",
  "SCALE_TYP": "string",
  "METHOD_TYP": "string",
  "CLASS": "string",
  "STATUS": "string",
  "CONSUMER_NAME": "string"
}
```

### Medical History Addiction Model
```json
{
  "id": "string",
  "patientId": "string",
  "diagnosis": [
    {
      "diseaseName": "string",
      "yearOfDiagnosis": "string",
      "diagnosisDuration": "string",
      "status": "active|inactive",
      "treatmentHistory": "string"
    }
  ],
  "smoking": {
    "history": "no|former|current",
    "count": "string",
    "frequency": "daily|2-3_times_week|4_times_month|occasionally"
  },
  "alcohol": {
    "history": "no|former|current",
    "count": "string",
    "frequency": "daily|2-3_times_week|4_times_month|occasionally"
  },
  "tobacco": {
    "history": "no|former|current",
    "count": "string",
    "frequency": "daily|2-3_times_week|4_times_month|occasionally"
  },
  "drugs": {
    "history": "no|former|current",
    "count": "string",
    "frequency": "daily|2-3_times_week|4_times_month|occasionally"
  },
  "nicotineDependenceTest": {
    "id": "string",
    "responses": {
      "timeToFirstCigarette": "string",
      "findDifficult": "string",
      "whichCigarette": "string",
      "cigarettesPerDay": "string",
      "moreFrequentMorning": "string",
      "smokeWhenIll": "string"
    },
    "testDate": "ISO 8601 datetime"
  },
  "created_on": "ISO 8601 datetime",
  "updated_on": "ISO 8601 datetime",
  "create_by": "string",
  "update_by": "string"
}
```

## Permission System

The API uses a comprehensive permission system with the following permission keys:

### EMR Module Permissions
- `emr.access` - Access EMR module
- `emr.patientinfo.view` - View patient information
- `emr.patientinfo.edit` - Edit patient information
- `emr.consultation.view` - View consultations
- `emr.consultation.manage` - Manage consultations
- `emr.prescription.view` - View prescriptions
- `emr.prescription.manage` - Manage prescriptions
- `emr.lab-test.view` - View lab tests
- `emr.lab-test.manage` - Manage lab tests
- `emr.doctorprofile.view` - View doctor profiles
- `emr.doctorprofile.edit` - Edit doctor profiles

### MRD Module Permissions
- `mrd.access` - Access MRD module
- `mrd.manage-patient.view` - View patient administrative data
- `mrd.manage-patient.edit` - Edit patient administrative data
- `mrd.patient-queue.manage` - Manage patient queue

### Organization Permissions
- `organization.manage` - Manage organizations
- `organization.patients.view` - View organization patients

### Role and Permission Management
- `role.manage` - Manage roles
- `permission.manage` - Manage permissions

### Dashboard Permissions
- `dashboard.view` - View dashboard

## Environment Configuration

The API behavior can be configured using environment variables:

- `TENANT_NAME` - Azure B2C tenant name
- `BASE_URL` - Application base URL
- `COSMOS_DB_ENDPOINT` - Cosmos DB endpoint
- `COSMOS_DB_KEY` - Cosmos DB access key
- `REDIS_CONNECTION_STRING` - Redis cache connection
- `BLOB_STORAGE_CONNECTION` - Azure Blob Storage connection
- `EMAIL_SERVICE_CONFIG` - Email service configuration

## Status Codes and Constants

### Lab Test Status
- `Not Paid` - Test not yet paid for
- `Awaited` - Waiting for test execution
- `Ready` - Test completed, results ready
- `Upload` - Results being uploaded
- `Uploaded` - Results uploaded and available

### Record Status
- `editable` - Record can be modified
- `finalized` - Record is finalized and cannot be modified

### Package Types
- `user` - User-specific package
- `department` - Department-specific package

## API Examples

### Complete Patient Registration Flow

1. **Create Organization** (if new)
```bash
curl -X POST "https://api.example.com/organization" \
  -H "Authorization: Bearer <token>" \
  -H "Content-Type: application/json" \
  -d '{
    "name": "City Hospital",
    "contactEmail": "<EMAIL>",
    "contactPersonName": "John Admin",
    "contactPhone": "+**********"
  }'
```

2. **Create Doctor User**
```bash
curl -X POST "https://api.example.com/user" \
  -H "Authorization: Bearer <token>" \
  -H "Content-Type: application/json" \
  -d '{
    "email": "<EMAIL>",
    "name": "Dr. Smith",
    "userRole": "doctor",
    "organizationId": "org-123"
  }'
```

3. **Create Doctor Profile**
```bash
curl -X POST "https://api.example.com/doctor" \
  -H "Authorization: Bearer <token>" \
  -H "Content-Type: application/json" \
  -d '{
    "username": "drsmith",
    "general": {
      "fullName": "Dr. John Smith",
      "designation": "Cardiologist",
      "department": "Cardiology"
    }
  }'
```

4. **Register Patient**
```bash
curl -X POST "https://api.example.com/patient" \
  -H "Authorization: Bearer <token>" \
  -H "Content-Type: application/json" \
  -d '{
    "name": "Jane Doe",
    "sex": "Female",
    "dob": "1985-05-15",
    "contact": {
      "phone": "+**********",
      "email": "<EMAIL>"
    }
  }'
```

5. **Create Appointment**
```bash
curl -X POST "https://api.example.com/appointment?doctorId=doctor-123" \
  -H "Authorization: Bearer <token>" \
  -H "Content-Type: application/json" \
  -d '{
    "patientId": "patient-456",
    "appointmentDate": "2024-01-15",
    "appointmentTime": "10:00",
    "reason": "Regular checkup"
  }'
```

6. **Record Medical History Addiction**
```bash
curl -X POST "https://api.example.com/patient/lifestyle/medical-history-addiction?patientId=patient-456" \
  -H "Authorization: Bearer <token>" \
  -H "Content-Type: application/json" \
  -d '{
    "diagnosis": [
      {
        "diseaseName": "Hypertension",
        "yearOfDiagnosis": "2020",
        "diagnosisDuration": "3 Years",
        "status": "active",
        "treatmentHistory": "Medication and lifestyle changes"
      }
    ],
    "smoking": {
      "history": "former",
      "count": "10",
      "frequency": "daily"
    },
    "alcohol": {
      "history": "current",
      "count": "2-3",
      "frequency": "2-3_times_week"
    },
    "tobacco": {
      "history": "no"
    },
    "drugs": {
      "history": "no"
    }
  }'
```

### Lab Test Ordering Flow

1. **Search Available Tests**
```bash
curl -X POST "https://api.example.com/lab-tests/search" \
  -H "Authorization: Bearer <token>" \
  -H "Content-Type: application/json" \
  -d '{
    "searchTerm": "blood",
    "department": "Hematology"
  }'
```

2. **Order Tests for Patient**
```bash
curl -X POST "https://api.example.com/patient-lab-test?patientId=patient-456" \
  -H "Authorization: Bearer <token>" \
  -H "Content-Type: application/json" \
  -d '{
    "tests": [
      {
        "testId": "test-123",
        "testName": "Complete Blood Count",
        "urgency": "routine"
      }
    ],
    "orderedBy": "doctor-123"
  }'
```

3. **Upload Test Results**
```bash
curl -X POST "https://api.example.com/lab-report/upload" \
  -H "Authorization: Bearer <token>" \
  -F "file=@lab-report.pdf" \
  -F "patientId=patient-456" \
  -F "testId=test-123"
```

## Bulk Operations

### LOINC Test Updates

For large-scale LOINC test updates, the API supports both synchronous and asynchronous processing:

**Small Updates (< 500 records)**: Processed synchronously
**Large Updates (≥ 500 records)**: Processed asynchronously with job tracking

```bash
# Start bulk update
curl -X POST "https://api.example.com/loinc/update" \
  -H "Authorization: Bearer <token>" \
  -H "Content-Type: application/json" \
  -d '{
    "organizationId": "org-123",
    "selectAll": true,
    "department": "Clinical Chemistry"
  }'

# Check status (for async operations)
curl -X GET "https://api.example.com/loinc/update/status/job-id" \
  -H "Authorization: Bearer <token>"
```

## Error Handling Examples

### Validation Errors
```json
{
  "error": "Validation failed",
  "details": [
    {
      "field": "email",
      "message": "Invalid email format"
    },
    {
      "field": "dob",
      "message": "Date of birth is required"
    }
  ],
  "status": 400
}
```

### Authentication Errors
```json
{
  "error": "Missing authorization token",
  "status": 401
}
```

### Permission Errors
```json
{
  "error": "User does not have permission",
  "requiredPermission": "emr.patientinfo.edit",
  "status": 403
}
```

### Payment APIs

#### Create Payment Order
**POST** `/payments/create-order`

Create a new payment order for various services.

**Request Body:**
```json
{
  "amount": 1000,
  "currency": "INR",
  "paymentType": "consultation|registration|prescription|lab_test",
  "patientId": "patient-id",
  "organizationId": "org-id",
  "description": "Payment description"
}
```

**Response:**
```json
{
  "data": {
    "orderId": "order_123",
    "amount": 1000,
    "currency": "INR",
    "key": "rzp_test_key"
  },
  "status": 200
}
```

#### Verify Payment
**POST** `/payments/verify`

Verify payment after successful transaction.

**Request Body:**
```json
{
  "razorpay_order_id": "order_123",
  "razorpay_payment_id": "pay_123",
  "razorpay_signature": "signature_hash"
}
```

#### Payment Webhook
**POST** `/payments/webhook`

Handle Razorpay webhook notifications (anonymous access).

#### Get Payment Details
**GET** `/payments/details?paymentId={paymentId}`

Get details of a specific payment.

#### Get Organization Payments
**GET** `/payments/organization?organizationId={organizationId}`

Get all payments for an organization with pagination support.

**Query Parameters:**
- `organizationId` (string, optional): Filter by organization
- `page` (number, optional): Page number (default: 1)
- `pageSize` (number, optional): Items per page (default: 10)
- `continueToken` (string, optional): Continuation token for pagination

### ABDM Integration APIs

#### Health Check
**GET** `/abdm?operation=health`

Check ABDM service health status.

**Response:**
```json
{
  "data": {
    "status": "healthy",
    "service": "ABDM Integration",
    "version": "1.0.0",
    "timestamp": "2024-01-01T00:00:00.000Z"
  },
  "status": 200
}
```

#### Initiate ABHA Creation by Aadhaar
**POST** `/abdm/initiate/aadhaar`
**POST** `/abdm?operation=initiate-aadhaar`

Initiate ABHA number creation using Aadhaar.

**Request Body:**
```json
{
  "aadhaar": "**********12"
}
```

#### Verify OTP for ABHA Creation
**POST** `/abdm?operation=verify-otp`

Verify OTP received for ABHA creation.

**Request Body:**
```json
{
  "txnId": "transaction-id",
  "otp": "123456"
}
```

#### Complete ABHA Creation
**POST** `/abdm?operation=complete-creation`

Complete ABHA number creation process.

#### Get ABHA Details by Number
**POST** `/abdm?operation=details-by-number`

Get ABHA details using ABHA number.

#### Get ABHA Details by Mobile
**POST** `/abdm?operation=details-by-mobile`

Get ABHA details using mobile number.

#### Verify ABHA Number
**POST** `/abdm?operation=verify-number`

Verify an existing ABHA number.

#### Resend OTP
**POST** `/abdm?operation=resend-otp`

Resend OTP for ABHA operations.

#### Verify OTP and Fetch Details by Number
**POST** `/abdm?operation=verify-otp-fetch-details`

Verify OTP and fetch ABHA details by number.

#### Request OTP for Mobile Profile Verification
**POST** `/abdm?operation=request-otp-mobile-profile`

Request OTP for mobile profile verification.

#### Verify OTP and Fetch Details by Mobile
**POST** `/abdm?operation=verify-otp-fetch-details-by-mobile`

Verify OTP and fetch ABHA details by mobile.

### Patient Consultation APIs

#### Get Patient Consultations
**GET** `/patient-consultation?patientId={patientId}`

Get consultation history for a patient.

#### Create Patient Consultation
**POST** `/patient-consultation?patientId={patientId}`

Create a new consultation record.

**Request Body:**
```json
{
  "doctorId": "doctor-id",
  "consultationDate": "2024-01-01",
  "chiefComplaint": "Patient complaint",
  "diagnosis": "Diagnosis details",
  "treatment": "Treatment plan",
  "notes": "Additional notes"
}
```

#### Update Patient Consultation
**PUT** `/patient-consultation?patientId={patientId}`

Update existing consultation record.

### Patient Vitals APIs

#### Get Patient Vitals
**GET** `/patient/vitals?patientId={patientId}`

Get vital signs for a patient.

#### Create Patient Vitals
**POST** `/patient/vitals?patientId={patientId}`

Record new vital signs for a patient.

**Request Body:**
```json
{
  "temperature": 98.6,
  "bloodPressure": {
    "systolic": 120,
    "diastolic": 80
  },
  "heartRate": 72,
  "respiratoryRate": 16,
  "oxygenSaturation": 98,
  "weight": 70.5,
  "height": 175,
  "bmi": 23.0,
  "recordedDate": "2024-01-01T10:00:00Z"
}
```

#### Update Patient Vitals
**PUT** `/patient/vitals?patientId={patientId}`

Update existing vital signs record.

## Testing

### Postman Collection
A comprehensive Postman collection is available for testing all API endpoints:
- Download: [EMR API Postman Collection](https://api.example.com/postman-collection)
- Environment variables included for easy setup

### Patient Lifestyle APIs

#### Get Patient Lifestyle
**GET** `/patient/lifestyle?patientId={patientId}&source={source}`

Get lifestyle information for a patient by source.

**Query Parameters:**
- `patientId` (string, required): Patient ID
- `source` (string, required): Lifestyle source/category
- `fromDate` (string, optional): Start date filter
- `toDate` (string, optional): End date filter

#### Create Patient Lifestyle
**POST** `/patient/lifestyle?patientId={patientId}`

Create new lifestyle record for a patient.

#### Update Patient Lifestyle
**PATCH** `/patient/lifestyle?id={id}`

Update existing lifestyle record.

### Patient Lifestyle Note APIs

#### Get Patient Lifestyle Notes
**GET** `/patient/lifestyle/note?patientId={patientId}`

Get lifestyle notes for a patient.

#### Create Patient Lifestyle Note
**POST** `/patient/lifestyle/note?patientId={patientId}`

Create new lifestyle note for a patient.

#### Update Patient Lifestyle Note
**PATCH** `/patient/lifestyle/note?patientId={patientId}`

Update existing lifestyle note.

### Patient Demographics APIs

#### Get Patient Demographics
**GET** `/patient/lifestyle/demographics?patientId={patientId}`

Get demographic information for a patient.

#### Create Patient Demographics
**POST** `/patient/lifestyle/demographics?patientId={patientId}`

Create demographic record for a patient.

**Request Body:**
```json
{
  "age": 30,
  "gender": "male|female|other",
  "maritalStatus": "single|married|divorced|widowed",
  "occupation": "Software Engineer",
  "education": "Bachelor's Degree",
  "income": "50000-100000",
  "address": {
    "street": "123 Main St",
    "city": "City Name",
    "state": "State",
    "zipCode": "12345",
    "country": "Country"
  }
}
```

#### Update Patient Demographics
**PUT** `/patient/lifestyle/demographics?patientId={patientId}`

Update existing demographic record.

### Patient History APIs

#### Get Patient History
**GET** `/patient/history?patientId={patientId}`

Get medical history for a patient.

**Query Parameters:**
- `patientId` (string, required): Patient ID
- `startDate` (string, optional): Start date filter
- `endDate` (string, optional): End date filter

#### Create Patient History
**POST** `/patient/history?patientId={patientId}`

Create new medical history record.

**Request Body:**
```json
{
  "medicalConditions": ["Diabetes", "Hypertension"],
  "surgicalHistory": ["Appendectomy"],
  "allergies": ["Penicillin", "Peanuts"],
  "medications": ["Metformin", "Lisinopril"],
  "familyHistory": {
    "diabetes": true,
    "heartDisease": false,
    "cancer": true
  },
  "socialHistory": {
    "smoking": false,
    "alcohol": "occasional",
    "exercise": "regular"
  }
}
```

#### Update Patient History
**PUT** `/patient/history?patientId={patientId}`

Update existing medical history record.

### Medical History Addiction APIs

#### Get Medical History Addiction
**GET** `/patient/lifestyle/medical-history-addiction?patientId={patientId}`

Get addiction history for a patient.

#### Create Medical History Addiction
**POST** `/patient/lifestyle/medical-history-addiction?patientId={patientId}`

Create addiction history record.

**Request Body:**
```json
{
  "substanceType": "alcohol|tobacco|drugs|other",
  "frequency": "daily|weekly|monthly|occasional|former",
  "duration": "6 months",
  "quantity": "1 pack per day",
  "attempts_to_quit": 3,
  "last_quit_date": "2023-01-01",
  "treatment_history": "Counseling sessions"
}
```

#### Update Medical History Addiction
**PUT** `/patient/lifestyle/medical-history-addiction?id={id}`

Update existing addiction history record.

#### Delete Medical History Addiction
**DELETE** `/patient/lifestyle/medical-history-addiction?id={id}`

Delete addiction history record.

### Lifestyle Question APIs

#### Get Lifestyle Questions
**GET** `/lifestyle/question?source={source}`

Get lifestyle questions by source.

**Query Parameters:**
- `source` (string, required): Question source/category
- `section` (string, optional): Specific section filter

#### Create Lifestyle Question
**POST** `/lifestyle/question?source={source}`

Create new lifestyle question.

**Request Body:**
```json
{
  "section": "general",
  "questions": [
    {
      "id": "q1",
      "text": "Do you exercise regularly?",
      "type": "boolean|text|select|number",
      "options": ["Yes", "No"],
      "required": true
    }
  ]
}
```

#### Update Lifestyle Question
**PATCH** `/lifestyle/question?id={id}`

Update existing lifestyle question.

#### Delete Lifestyle Question
**DELETE** `/lifestyle/question?id={id}`

Delete lifestyle question.

### Test Data
Sample test data is available for development and testing:
- Organizations, users, patients, doctors
- Lab tests, medicines, appointments
- Realistic data for comprehensive testing

### Lifestyle Ambient Listening APIs

#### Process Lifestyle Ambient Listening
**POST** `/lifestyle/ambient-listening`

Process ambient listening transcript for lifestyle information extraction.

**Request Body:**
```json
{
  "transcript": "Patient mentions exercising 3 times a week and eating healthy meals...",
  "source": "lifestyle_assessment"
}
```

**Response:**
```json
{
  "data": {
    "conversation": [
      {
        "speaker": "patient",
        "text": "I exercise 3 times a week"
      }
    ],
    "summary": {
      "exercise_frequency": "3 times per week",
      "diet_habits": "healthy meals"
    }
  },
  "status": 200
}
```

### Lifestyle Summary APIs

#### Generate Lifestyle Summary
**POST** `/lifestyle/summary`

Generate AI-powered summary from lifestyle transcript (anonymous access).

**Request Body:**
```json
{
  "transcript": "Patient conversation transcript...",
  "source": "lifestyle_assessment"
}
```

### Nurse APIs

#### Get Nurse by ID
**GET** `/nurse?id={nurseId}`

Get nurse profile by ID.

#### Create Nurse Profile
**POST** `/nurse`

Create new nurse profile.

**Request Body:**
```json
{
  "firstName": "Jane",
  "lastName": "Smith",
  "email": "<EMAIL>",
  "phone": "+**********",
  "licenseNumber": "RN123456",
  "department": "Emergency",
  "shift": "day|night|rotating",
  "experience": 5
}
```

#### Update Nurse Profile
**PUT** `/nurse`

Update existing nurse profile.

### Queue Management APIs

#### Get Queue by ID
**GET** `/queue?queueId={queueId}`

Get queue details by ID.

#### Update Queue
**PATCH** `/queue?queueId={queueId}`

Update queue information.

**Request Body:**
```json
{
  "status": "waiting|in_progress|completed|cancelled",
  "priority": "low|normal|high|urgent",
  "estimatedWaitTime": 30,
  "notes": "Patient requires wheelchair assistance"
}
```

### Appointment Queue APIs

#### Get Appointment Queue
**GET** `/appointment/queue?doctorId={doctorId}`

Get appointment queue for a doctor.

#### Add to Appointment Queue
**POST** `/appointment/queue`

Add patient to appointment queue.

#### Update Appointment Queue
**PATCH** `/appointment/queue?queueId={queueId}`

Update appointment queue entry.

### Book Consultation APIs

#### Get Future Appointments
**GET** `/book-consultation/future?patientId={patientId}`

Get future appointments for a patient.

### Test Package APIs

#### Get Test Packages
**GET** `/test-package?type={packageType}`

Get test packages by type.

**Query Parameters:**
- `type` (string, optional): Package type filter
- `organizationId` (string, optional): Organization filter

#### Create Test Package
**POST** `/test-package`

Create new test package.

**Request Body:**
```json
{
  "name": "Basic Health Checkup",
  "description": "Comprehensive basic health screening",
  "type": "screening|diagnostic|preventive",
  "tests": ["CBC", "Lipid Profile", "Blood Sugar"],
  "price": 2500,
  "duration": "2 hours",
  "preparationInstructions": "12 hour fasting required"
}
```

#### Update Test Package
**PATCH** `/test-package?packageId={packageId}`

Update existing test package.

#### Get Tests for Package
**GET** `/test-package/tests?packageId={packageId}`

Get tests included in a specific package.

### Prescription Package APIs

#### Get Prescription Packages
**GET** `/prescription-package?type={packageType}`

Get prescription packages by type.

#### Create Prescription Package
**POST** `/prescription-package`

Create new prescription package.

**Request Body:**
```json
{
  "name": "Diabetes Management Kit",
  "description": "Complete diabetes medication package",
  "type": "chronic|acute|preventive",
  "medicines": [
    {
      "medicineId": "med-123",
      "quantity": 30,
      "dosage": "500mg",
      "frequency": "twice daily"
    }
  ],
  "totalPrice": 1500,
  "duration": "30 days"
}
```

#### Get Prescription Package Details
**GET** `/prescription-package/details?packageId={packageId}`

Get detailed information about a prescription package.


### Lab Report Document APIs

#### Upload Lab Report
**POST** `/lab-report/upload`

Upload encrypted lab report documents.

**Request Body (multipart/form-data):**
```
files: [lab-report.pdf, lab-report-2.jpg]
patientId: patient-id
labTestId: test-id
```

**Response:**
```json
{
  "data": {
    "uploadedFiles": [
      {
        "docId": "doc-123",
        "fileName": "lab-report.pdf",
        "fileSize": 1024000,
        "uploadDate": "2024-01-01T10:00:00Z"
      }
    ]
  },
  "status": 200
}
```

#### Preview Lab Report
**GET** `/lab-report/preview?docId={docId}`

Preview uploaded lab report (returns decrypted file).

### Document Upload APIs

#### Upload User Document
**POST** `/user/document/upload`

Upload user-related documents.

**Request Body (multipart/form-data):**
```
file: [document.pdf]
userId: user-id
doc_type: license|certificate|id_proof
```

**File Size Limit:** 200MB

### Proxy APIs

#### ICD Proxy
**GET** `/icd-proxy?q={searchTerm}`

Proxy for ICD (International Classification of Diseases) API.

**Query Parameters:**
- `q` (string, required): Search term for ICD codes

**Response:**
```json
{
  "data": {
    "destinationEntities": [
      {
        "id": "123456",
        "title": "Essential hypertension",
        "theCode": "I10"
      }
    ]
  },
  "status": 200
}
```

#### SNOMED Proxy
**GET** `/snomed-proxy?term={searchTerm}`

Proxy for SNOMED CT terminology API.

**Query Parameters:**
- `term` (string, required): Search term for SNOMED codes

### Summary APIs

#### Generate Medical Summary
**POST** `/summary`

Generate AI-powered medical summary from conversation transcript (anonymous access).

**Request Body:**
```
Patient conversation transcript as plain text
```

**Response:**
```json
{
  "data": {
    "conversation": [
      {
        "speaker": "doctor",
        "text": "How are you feeling today?"
      },
      {
        "speaker": "patient",
        "text": "I have been experiencing chest pain"
      }
    ],
    "summary": {
      "chief_complaint": "Chest pain",
      "symptoms": ["chest pain"],
      "diagnosis": null,
      "treatment_plan": null
    }
  },
  "status": 200
}
```

### Doctor Summary APIs

#### Get Doctor Summary
**GET** `/doctor/summary?patientId={patientId}`

Get doctor's summary for a patient.

#### Create Doctor Summary
**POST** `/doctor/summary?patientId={patientId}`

Create doctor's summary for a patient.

#### Update Doctor Summary
**PATCH** `/doctor/summary?patientId={patientId}`

Update doctor's summary for a patient.

### User Signup API

#### User Signup (Integration)
**POST** `/usersignup`

Create user account via integration (requires Basic Auth).

**Authentication:** Basic Auth with credentials:
- Username: `emr_integration`
- Password: `Letmegetin@2436`

**Request Body:**
```json
{
  "firstName": "John",
  "lastName": "Doe",
  "email": "<EMAIL>",
  "organizationId": "org-123",
  "roleId": "role-456"
}
```


## API Coverage Summary

This documentation covers **80+ API endpoints** across the following modules:

### Core Modules
- **Authentication & User Management** (8 endpoints)
- **Organization Management** (6 endpoints)
- **Patient Management** (12 endpoints)
- **Doctor Management** (8 endpoints)
- **Appointment Management** (10 endpoints)

### Medical Modules
- **Medicine Management** (6 endpoints)
- **Lab Test Management** (8 endpoints)
- **Prescription Management** (6 endpoints)
- **Patient Vitals** (3 endpoints)
- **Patient Consultation** (3 endpoints)

### Advanced Features
- **Payment Integration** (5 endpoints)
- **ABDM Integration** (10 endpoints)
- **Lifestyle Management** (15 endpoints)
- **Queue Management** (4 endpoints)
- **Document Management** (4 endpoints)

### AI & Analytics
- **Ambient Listening** (2 endpoints)
- **Medical Summaries** (4 endpoints)
- **Proxy Services** (2 endpoints)

### Administrative
- **Dashboard & Analytics** (3 endpoints)
- **Role & Permission Management** (4 endpoints)
- **Package Management** (6 endpoints)

